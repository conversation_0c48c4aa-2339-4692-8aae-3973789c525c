/**
 * Utility functions for handling winner-related operations
 */
const winnerUtils = module.exports;

/**
 * Get the winning player from game results array
 * @param {Array} gameResults - Array of game results containing player information
 * @returns {Object|null} The winning player object or null if no winner found
 */
winnerUtils.getWinningPlayer = function(gameResults) {
    if (!gameResults || !Array.isArray(gameResults) || gameResults.length === 0) {
        return null;
    }

    // Loop through each game result
    for (let i = 0; i < gameResults.length; i++) {
        const result = gameResults[i];
        
        // Check if players array exists
        if (result.players && Array.isArray(result.players)) {
            // Find the winning player
            const winner = result.players.find(player => player.isWinner === true);
            if (winner) {
                return winner;
            }
        }
    }

    return null;
};

/**
 * Get all winning players from game results array
 * @param {Array} gameResults - Array of game results containing player information
 * @returns {Array} Array of winning player objects
 */
winnerUtils.getAllWinningPlayers = function(gameResults) {
    const winners = [];
    
    if (!gameResults || !Array.isArray(gameResults) || gameResults.length === 0) {
        return winners;
    }

    // Loop through each game result
    for (let i = 0; i < gameResults.length; i++) {
        const result = gameResults[i];
        
        // Check if players array exists
        if (result.players && Array.isArray(result.players)) {
            // Find all winning players
            const resultWinners = result.players.filter(player => player.isWinner === true);
            winners.push(...resultWinners);
        }
    }

    return winners;
};

